{"timestamp":"2025-06-16 14:15:47","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"60d79286-bda2-4d29-a3ba-90c9d2ab26ae","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:15:47","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"60d79286-bda2-4d29-a3ba-90c9d2ab26ae","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:18:47","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"c7adcb23-9ba3-49b0-82d2-a7ee9d3e8472","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:18:47","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"c7adcb23-9ba3-49b0-82d2-a7ee9d3e8472","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:19:15","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"670937d7-857c-47db-8f04-4b0c84465bb2","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:19:15","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"670937d7-857c-47db-8f04-4b0c84465bb2","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:19:30","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"b29bb6c4-b89f-4df2-85be-1918a4e585f2","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:19:30","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"b29bb6c4-b89f-4df2-85be-1918a4e585f2","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:19:30","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"3fb47c1a-0729-43d2-97be-3a41fc29ee63","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:19:30","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"INSURANCE-SERVICE-LIMITS","entityId":"check","details":{"method":"POST","url":"/insurance-service-limits/check","statusCode":200},"requestId":"3fb47c1a-0729-43d2-97be-3a41fc29ee63","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/insurance-service-limits/check"}
{"timestamp":"2025-06-16 14:19:33","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"SCHEDULINGS","entityId":"check-availability","details":{"method":"POST","url":"/schedulings/check-availability","statusCode":200},"requestId":"943897ad-0909-4830-bbcf-6f031f03013b","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/schedulings/check-availability"}
{"timestamp":"2025-06-16 14:19:33","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"SCHEDULINGS","entityId":"check-availability","details":{"method":"POST","url":"/schedulings/check-availability","statusCode":200},"requestId":"943897ad-0909-4830-bbcf-6f031f03013b","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/schedulings/check-availability"}
{"timestamp":"2025-06-16 14:19:34","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"SCHEDULINGS","entityId":"check-patient-availability","details":{"method":"POST","url":"/schedulings/check-patient-availability","statusCode":200},"requestId":"85b72a4a-8d1b-4679-bb6a-b648fbad7361","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/schedulings/check-patient-availability"}
{"timestamp":"2025-06-16 14:19:34","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"SCHEDULINGS","entityId":"check-patient-availability","details":{"method":"POST","url":"/schedulings/check-patient-availability","statusCode":200},"requestId":"85b72a4a-8d1b-4679-bb6a-b648fbad7361","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/schedulings/check-patient-availability"}
{"timestamp":"2025-06-16 14:19:34","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"SCHEDULINGS","entityId":"check-availability","details":{"method":"POST","url":"/schedulings/check-availability","statusCode":200},"requestId":"21442eb9-8758-4b12-b6ec-6165b774481f","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/schedulings/check-availability"}
{"timestamp":"2025-06-16 14:19:34","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"SCHEDULINGS","entityId":"check-availability","details":{"method":"POST","url":"/schedulings/check-availability","statusCode":200},"requestId":"21442eb9-8758-4b12-b6ec-6165b774481f","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/schedulings/check-availability"}
{"timestamp":"2025-06-16 14:19:34","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"SCHEDULINGS","entityId":"check-patient-availability","details":{"method":"POST","url":"/schedulings/check-patient-availability","statusCode":200},"requestId":"ca71eefd-3f67-4b0f-b680-034b1be8febd","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/schedulings/check-patient-availability"}
{"timestamp":"2025-06-16 14:19:34","level":"info","message":"AUDIT_EVENT","action":"CREATE","entityType":"SCHEDULINGS","entityId":"check-patient-availability","details":{"method":"POST","url":"/schedulings/check-patient-availability","statusCode":200},"requestId":"ca71eefd-3f67-4b0f-b680-034b1be8febd","ip":"**********","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********","method":"POST","url":"/schedulings/check-patient-availability"}
