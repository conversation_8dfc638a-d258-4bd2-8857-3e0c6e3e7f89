globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/people/insurances/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/ClientLayout.js":{"*":{"id":"(ssr)/./src/app/ClientLayout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/landing/layout.js":{"*":{"id":"(ssr)/./src/app/landing/layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/landing/page.js":{"*":{"id":"(ssr)/./src/app/landing/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/subscription/signup/page.js":{"*":{"id":"(ssr)/./src/app/subscription/signup/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.js":{"*":{"id":"(ssr)/./src/app/dashboard/layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/admin/introduction/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/admin/introduction/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/people/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/clients/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/people/clients/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/persons/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/people/persons/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/insurances/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/people/insurances/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/insurance-limits/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/people/insurance-limits/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/admin/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/admin/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/scheduler/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/scheduler/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/scheduler/introduction/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/scheduler/introduction/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/scheduler/calendar/page.js":{"*":{"id":"(ssr)/./src/app/dashboard/scheduler/calendar/page.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\ClientLayout.js":{"id":"(app-pages-browser)/./src/app/ClientLayout.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\landing\\layout.js":{"id":"(app-pages-browser)/./src/app/landing/layout.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\landing\\page.js":{"id":"(app-pages-browser)/./src/app/landing/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\subscription\\signup\\page.js":{"id":"(app-pages-browser)/./src/app/subscription/signup/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\layout.js":{"id":"(app-pages-browser)/./src/app/dashboard/layout.js","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\admin\\introduction\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/admin/introduction/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/page.js","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\people\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/people/page.js","name":"*","chunks":["app/dashboard/people/page","static/chunks/app/dashboard/people/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\people\\clients\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/people/clients/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\people\\persons\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/people/persons/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\people\\insurances\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/people/insurances/page.js","name":"*","chunks":["app/dashboard/people/insurances/page","static/chunks/app/dashboard/people/insurances/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\people\\insurance-limits\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/people/insurance-limits/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\admin\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/admin/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\scheduler\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/scheduler/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\scheduler\\introduction\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/scheduler/introduction/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\scheduler\\calendar\\page.js":{"id":"(app-pages-browser)/./src/app/dashboard/scheduler/calendar/page.js","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\layout":[{"inlined":false,"path":"static/css/app/dashboard/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\page":[{"inlined":false,"path":"static/css/app/dashboard/page.css"}],"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\people\\page":[],"C:\\Users\\<USER>\\Desktop\\Projeto X\\high-tide-systems-frontend\\src\\app\\dashboard\\people\\insurances\\page":[{"inlined":false,"path":"static/css/app/dashboard/people/insurances/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./src/app/ClientLayout.js":{"*":{"id":"(rsc)/./src/app/ClientLayout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/landing/layout.js":{"*":{"id":"(rsc)/./src/app/landing/layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/landing/page.js":{"*":{"id":"(rsc)/./src/app/landing/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/subscription/signup/page.js":{"*":{"id":"(rsc)/./src/app/subscription/signup/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.js":{"*":{"id":"(rsc)/./src/app/dashboard/layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/admin/introduction/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/admin/introduction/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/people/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/clients/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/people/clients/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/persons/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/people/persons/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/insurances/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/people/insurances/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/people/insurance-limits/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/people/insurance-limits/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/admin/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/admin/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/scheduler/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/scheduler/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/scheduler/introduction/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/scheduler/introduction/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/scheduler/calendar/page.js":{"*":{"id":"(rsc)/./src/app/dashboard/scheduler/calendar/page.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}